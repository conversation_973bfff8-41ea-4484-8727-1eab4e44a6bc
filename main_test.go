package main

import (
	"encoding/json"
	"testing"
)

func TestPrintJson(t *testing.T) {
	s := NewRPAMCPServer()
	jd := s.ToolsListPrettyJSON()
	println(jd)
}

func TestClick(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"x": 10.0, "y": 20.0, "button": "left"}
	res, _ := s.click(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("click failed: %v", obj)
	}
}

func TestDoubleClick(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"x": 10.0, "y": 20.0}
	res, _ := s.doubleClick(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.<PERSON>("doubleClick failed: %v", obj)
	}
}

func TestMoveMouse(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"x": 10.0, "y": 20.0}
	res, _ := s.moveMouse(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("moveMouse failed: %v", obj)
	}
}

func TestTypeText(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"text": "hello"}
	res, _ := s.typeText(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("typeText failed: %v", obj)
	}
}

func TestKeyPress(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"key": "enter"}
	res, _ := s.keyPress(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("keyPress failed: %v", obj)
	}
}

func TestScreenshot(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"filename": "test_screenshot.png"}
	res, _ := s.screenshot(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("screenshot failed: %v", obj)
	}
}

func TestGetScreenSize(t *testing.T) {
	s := NewRPAMCPServer()
	res, _ := s.getScreenSize(map[string]any{})
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["width"] == nil || obj["height"] == nil {
		t.Errorf("getScreenSize failed: %v", obj)
	}
}

func TestGetMousePos(t *testing.T) {
	s := NewRPAMCPServer()
	res, _ := s.getMousePos(map[string]any{})
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["x"] == nil || obj["y"] == nil {
		t.Errorf("getMousePos failed: %v", obj)
	}
}

func TestScroll(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"direction": "down", "clicks": 1, "x": 10.0, "y": 20.0}
	res, _ := s.scroll(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("scroll failed: %v", obj)
	}
}

func TestWait(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{"seconds": 0.1}
	res, _ := s.wait(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("wait failed: %v", obj)
	}
}

func TestDrag(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{
		"from_x": 10.0,
		"from_y": 20.0,
		"to_x":   30.0,
		"to_y":   40.0,
		"button": "left",
	}
	res, _ := s.drag(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("drag failed: %v", obj)
	}
	// 验证返回的坐标
	if obj["from_x"].(float64) != 10 || obj["from_y"].(float64) != 20 {
		t.Errorf("drag coordinates mismatch: expected from (10,20), got (%v,%v)", obj["from_x"], obj["from_y"])
	}
	if obj["to_x"].(float64) != 30 || obj["to_y"].(float64) != 40 {
		t.Errorf("drag coordinates mismatch: expected to (30,40), got (%v,%v)", obj["to_x"], obj["to_y"])
	}
}

func TestDragWithDefaultButton(t *testing.T) {
	s := NewRPAMCPServer()
	args := map[string]any{
		"from_x": 50.0,
		"from_y": 60.0,
		"to_x":   70.0,
		"to_y":   80.0,
	}
	res, _ := s.drag(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("drag with default button failed: %v", obj)
	}
	// 验证默认按钮是 left
	if obj["button"].(string) != "left" {
		t.Errorf("drag default button should be 'left', got: %v", obj["button"])
	}
}

func TestRunAppleScript(t *testing.T) {
	s := NewRPAMCPServer()
	// 使用一个简单的 AppleScript 命令来测试
	args := map[string]any{"script": "return \"test output\""}
	res, _ := s.runAppleScript(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("runAppleScript failed: %v", obj)
	}
	// 验证输出
	if obj["output"].(string) != "test output" {
		t.Errorf("runAppleScript output mismatch: expected 'test output', got: %v", obj["output"])
	}
}

func TestRunAppleScriptWithError(t *testing.T) {
	s := NewRPAMCPServer()
	// 使用一个会出错的 AppleScript 命令
	args := map[string]any{"script": "invalid_command_that_does_not_exist"}
	res, _ := s.runAppleScript(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["success"].(bool) {
		t.Errorf("runAppleScript should have failed with invalid script")
	}
	// 验证有错误信息
	if obj["error"] == nil {
		t.Errorf("runAppleScript should return error information")
	}
}

func TestOpenApp(t *testing.T) {
	s := NewRPAMCPServer()
	// 使用 Finder 作为测试应用，因为它在 macOS 上总是可用的
	args := map[string]any{"app_name": "Finder"}
	res, _ := s.openApp(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("openApp failed: %v", obj)
	}
	// 验证应用名称
	if obj["app_name"].(string) != "Finder" {
		t.Errorf("openApp app_name mismatch: expected 'Finder', got: %v", obj["app_name"])
	}
}

func TestOpenAppWithInvalidName(t *testing.T) {
	s := NewRPAMCPServer()
	// 使用一个不存在的应用名称
	args := map[string]any{"app_name": "NonExistentApp12345"}
	res, _ := s.openApp(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	// 这个测试可能成功也可能失败，取决于系统，但不应该崩溃
	// 主要是确保函数能正常处理错误情况
	if obj["app_name"].(string) != "NonExistentApp12345" {
		t.Errorf("openApp should preserve app_name in response")
	}
}

func TestGetAppList(t *testing.T) {
	s := NewRPAMCPServer()
	res, _ := s.getAppList(map[string]any{})
	var obj map[string]any

	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("getAppList failed: %v", obj)
	}
	// 验证返回的应用列表
	if obj["applications"] == nil {
		t.Errorf("getAppList should return applications list")
	}
	if obj["count"] == nil {
		t.Errorf("getAppList should return count")
	}

	// 验证应用列表是数组类型
	apps, ok := obj["applications"].([]any)
	if !ok {
		t.Errorf("getAppList applications should be an array")
	}

	// 验证计数与实际应用数量匹配
	count, ok := obj["count"].(float64)
	if !ok {
		t.Errorf("getAppList count should be a number")
	}
	if int(count) != len(apps) {
		t.Errorf("getAppList count mismatch: count=%v, actual length=%v", count, len(apps))
	}

	// 验证至少有一些应用在运行（比如 Finder）
	if len(apps) == 0 {
		t.Errorf("getAppList should return at least some running applications")
	}
}

// 测试参数验证
func TestDragMissingParameters(t *testing.T) {
	s := NewRPAMCPServer()
	// 缺少必需参数
	args := map[string]any{"from_x": 10.0, "from_y": 20.0}
	res, _ := s.drag(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["success"].(bool) {
		t.Errorf("drag should fail with missing parameters")
	}
}

func TestRunAppleScriptMissingScript(t *testing.T) {
	s := NewRPAMCPServer()
	// 缺少 script 参数
	args := map[string]any{}
	res, _ := s.runAppleScript(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["success"].(bool) {
		t.Errorf("runAppleScript should fail with missing script parameter")
	}
}

func TestOpenAppMissingAppName(t *testing.T) {
	s := NewRPAMCPServer()
	// 缺少 app_name 参数
	args := map[string]any{}
	res, _ := s.openApp(args)
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if obj["success"].(bool) {
		t.Errorf("openApp should fail with missing app_name parameter")
	}
}

// 测试所有新工具是否正确注册
func TestNewToolsRegistration(t *testing.T) {
	s := NewRPAMCPServer()

	// 检查新工具是否在工具列表中
	expectedNewTools := []string{"drag", "run_apple_script", "open_app", "get_app_list"}

	toolNames := make(map[string]bool)
	for _, tool := range s.tools {
		toolNames[tool.Name] = true
	}

	for _, expectedTool := range expectedNewTools {
		if !toolNames[expectedTool] {
			t.Errorf("New tool '%s' is not registered in tools list", expectedTool)
		}
	}

	// 检查新工具是否在工具函数映射中
	for _, expectedTool := range expectedNewTools {
		if _, exists := s.toolFuncs[expectedTool]; !exists {
			t.Errorf("New tool '%s' is not registered in toolFuncs map", expectedTool)
		}
	}

	// 验证总工具数量（原有10个 + 新增4个 = 14个）
	expectedTotalTools := 14
	if len(s.tools) != expectedTotalTools {
		t.Errorf("Expected %d tools, but got %d", expectedTotalTools, len(s.tools))
	}
}

// 测试通过 callTool 方法调用新工具
func TestCallNewToolsViaCallTool(t *testing.T) {
	s := NewRPAMCPServer()

	// 测试通过 callTool 调用 drag
	dragArgs := map[string]any{
		"from_x": 100.0,
		"from_y": 100.0,
		"to_x":   200.0,
		"to_y":   200.0,
	}
	res, err := s.callTool("drag", dragArgs)
	if err != nil {
		t.Errorf("callTool for drag failed: %v", err)
	}
	var obj map[string]any
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("drag via callTool failed: %v", obj)
	}

	// 测试通过 callTool 调用 get_app_list
	res, err = s.callTool("get_app_list", map[string]any{})
	if err != nil {
		t.Errorf("callTool for get_app_list failed: %v", err)
	}
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("get_app_list via callTool failed: %v", obj)
	}

	// 测试通过 callTool 调用 run_apple_script
	scriptArgs := map[string]any{"script": "return \"callTool test\""}
	res, err = s.callTool("run_apple_script", scriptArgs)
	if err != nil {
		t.Errorf("callTool for run_apple_script failed: %v", err)
	}
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("run_apple_script via callTool failed: %v", obj)
	}

	// 测试通过 callTool 调用 open_app
	appArgs := map[string]any{"app_name": "safari"}
	res, err = s.callTool("open_app", appArgs)
	if err != nil {
		t.Errorf("callTool for open_app failed: %v", err)
	}
	json.Unmarshal([]byte(res), &obj)
	if !obj["success"].(bool) {
		t.Errorf("open_app via callTool failed: %v", obj)
	}
}
