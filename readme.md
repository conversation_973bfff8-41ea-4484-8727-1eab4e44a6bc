# RPA SERVER 配置和使用说明

## 1. MCP 客户端配置

```json
{
  "mcpServers": {
    "rpa-server": {
      "command": "/path/to/rpa-mcp-server",
      "args": []
    }
  }
}
```

## 2. 支持的动作

### 鼠标操作
- `click`: 在指定坐标点击
- `double_click`: 在指定坐标双击
- `move_mouse`: 移动鼠标到指定坐标
- `scroll`: 在指定位置滚动
- `drag`: 从一个坐标拖拽到另一个坐标

### 键盘操作
- `type_text`: 输入文本
- `key_press`: 按键或组合键(快捷键)

### 屏幕操作
- `screenshot`: 截图
- `get_screen_size`: 获取屏幕尺寸
- `get_mouse_pos`: 获取鼠标位置

### 其他工具
- `wait`: 等待指定时间
- `run_apple_script`: 在 macOS 上执行 AppleScript 代码
- `open_app`: 通过应用程序名称打开应用
- `get_app_list`: 获取当前运行的应用程序列表

## 3. 使用示例

### 点击操作
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "click",
    "arguments": {
      "x": 100,
      "y": 200,
      "button": "left"
    }
  }
}
```

### 输入文本
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "type_text",
    "arguments": {
      "text": "Hello World!"
    }
  }
}
```

### 按键操作
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "key_press",
    "arguments": {
      "key": "ctrl+c"
    }
  }
}
```

### 截图
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "screenshot",
    "arguments": {
      "filename": "my_screenshot.png"
    }
  }
}
```



### 拖拽
**描述**: 从一个坐标拖拽到另一个坐标

**参数**:
- `from_x` (number, 必需): 起始 X 坐标
- `from_y` (number, 必需): 起始 Y 坐标  
- `to_x` (number, 必需): 目标 X 坐标
- `to_y` (number, 必需): 目标 Y 坐标
- `button` (string, 可选): 鼠标按钮，默认为 "left"

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "drag",
    "arguments": {
      "from_x": 100,
      "from_y": 100,
      "to_x": 200,
      "to_y": 200,
      "button": "left"
    }
  }
}
```

### 执行 AppleScript
**描述**: 在 macOS 上执行 AppleScript 代码

**参数**:
- `script` (string, 必需): 要执行的 AppleScript 代码

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "run_apple_script",
    "arguments": {
      "script": "display dialog \"Hello World!\" buttons {\"OK\"}"
    }
  }
}
```

### 打开应用程序
**描述**: 通过应用程序名称打开应用

**参数**:
- `app_name` (string, 必需): 要打开的应用程序名称

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "open_app",
    "arguments": {
      "app_name": "Safari"
    }
  }
}
```

### 获取应用程序列表
**描述**: 获取当前运行的应用程序列表

**参数**: 无

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "get_app_list",
    "arguments": {}
  }
}
```

**返回示例**:
```json
{
  "success": true,
  "applications": ["Finder", "Safari", "Terminal", "Chrome"],
  "count": 4,
  "message": "Found 4 running applications"
}
```
## TOOL-LIST和对应参数
```json
[
  {
    "name": "click",
    "description": "Click at specified coordinates",
    "inputSchema": {
      "properties": {
        "button": {
          "default": "left",
          "description": "Mouse button: left, right, middle",
          "type": "string"
        },
        "x": {
          "description": "X coordinate",
          "type": "number"
        },
        "y": {
          "description": "Y coordinate",
          "type": "number"
        }
      },
      "required": [
        "x",
        "y"
      ],
      "type": "object"
    }
  },
  {
    "name": "double_click",
    "description": "Double click at specified coordinates",
    "inputSchema": {
      "properties": {
        "x": {
          "description": "X coordinate",
          "type": "number"
        },
        "y": {
          "description": "Y coordinate",
          "type": "number"
        }
      },
      "required": [
        "x",
        "y"
      ],
      "type": "object"
    }
  },
  {
    "name": "move_mouse",
    "description": "Move mouse to specified coordinates",
    "inputSchema": {
      "properties": {
        "x": {
          "description": "X coordinate",
          "type": "number"
        },
        "y": {
          "description": "Y coordinate",
          "type": "number"
        }
      },
      "required": [
        "x",
        "y"
      ],
      "type": "object"
    }
  },
  {
    "name": "type_text",
    "description": "Type text at current cursor position",
    "inputSchema": {
      "properties": {
        "text": {
          "description": "Text to type",
          "type": "string"
        }
      },
      "required": [
        "text"
      ],
      "type": "object"
    }
  },
  {
    "name": "key_press",
    "description": "Press a key or key combination",
    "inputSchema": {
      "properties": {
        "key": {
          "description": "Key to press (e.g., 'enter', 'ctrl+c', 'alt+tab')",
          "type": "string"
        }
      },
      "required": [
        "key"
      ],
      "type": "object"
    }
  },
  {
    "name": "screenshot",
    "description": "Take a screenshot and save to file",
    "inputSchema": {
      "properties": {
        "filename": {
          "default": "screenshot.png",
          "description": "Filename to save screenshot (optional)",
          "type": "string"
        }
      },
      "type": "object"
    }
  },
  {
    "name": "get_screen_size",
    "description": "Get screen dimensions",
    "inputSchema": {
      "properties": {},
      "type": "object"
    }
  },
  {
    "name": "get_mouse_pos",
    "description": "Get current mouse position",
    "inputSchema": {
      "properties": {},
      "type": "object"
    }
  },
  {
    "name": "scroll",
    "description": "Scroll at specified coordinates",
    "inputSchema": {
      "properties": {
        "clicks": {
          "default": 3,
          "description": "Number of scroll clicks",
          "type": "number"
        },
        "direction": {
          "default": "down",
          "description": "Scroll direction: up, down, left, right",
          "type": "string"
        },
        "x": {
          "description": "X coordinate",
          "type": "number"
        },
        "y": {
          "description": "Y coordinate",
          "type": "number"
        }
      },
      "required": [
        "x",
        "y"
      ],
      "type": "object"
    }
  },
  {
    "name": "wait",
    "description": "Wait for specified duration",
    "inputSchema": {
      "properties": {
        "seconds": {
          "description": "Number of seconds to wait",
          "type": "number"
        }
      },
      "required": [
        "seconds"
      ],
      "type": "object"
    }
  },
  {
    "name": "drag",
    "description": "Drag from one coordinate to another",
    "inputSchema": {
      "properties": {
        "button": {
          "default": "left",
          "description": "Mouse button: left, right, middle",
          "type": "string"
        },
        "from_x": {
          "description": "Starting X coordinate",
          "type": "number"
        },
        "from_y": {
          "description": "Starting Y coordinate",
          "type": "number"
        },
        "to_x": {
          "description": "Ending X coordinate",
          "type": "number"
        },
        "to_y": {
          "description": "Ending Y coordinate",
          "type": "number"
        }
      },
      "required": [
        "from_x",
        "from_y",
        "to_x",
        "to_y"
      ],
      "type": "object"
    }
  },
  {
    "name": "run_apple_script",
    "description": "Execute AppleScript code on macOS",
    "inputSchema": {
      "properties": {
        "script": {
          "description": "AppleScript code to execute",
          "type": "string"
        }
      },
      "required": [
        "script"
      ],
      "type": "object"
    }
  },
  {
    "name": "open_app",
    "description": "Open an application by name",
    "inputSchema": {
      "properties": {
        "app_name": {
          "description": "Name of the application to open",
          "type": "string"
        }
      },
      "required": [
        "app_name"
      ],
      "type": "object"
    }
  },
  {
    "name": "get_app_list",
    "description": "Get list of running applications",
    "inputSchema": {
      "properties": {},
      "type": "object"
    }
  }
]
```